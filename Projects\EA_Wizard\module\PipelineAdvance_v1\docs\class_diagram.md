# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 📋 完整模組列表與方法註解

### 🔧 核心介面模組 (Interface)

## 📁 模組結構

```
PipelineAdvance_v1/
├── interface/                                  # 介面定義資料夾
│   ├── ITradingPipeline.mqh                   # 交易流水線介面
│   └── ITradingPipelineDriver.mqh             # 交易流水線驅動器介面
├── docs/                                      # 文檔目錄
├── test/                                      # 測試目錄
├── examples/                                  # 使用示例
├── feature/                                   # 功能擴展目錄
├── TradingPipeline.mqh                        # 抽象基類
├── TradingPipelineContainer*.mqh              # 容器相關類
├── TradingPipelineDriver*.mqh                 # 驅動器相關類
├── TradingPipelineRegistry.mqh                # 註冊器
├── TradingPipelineExplorer.mqh                # 探索器
├── TradingController.mqh                      # EA 生命週期控制器
├── TradingErrorHandler.mqh                    # 錯誤處理器
└── ObjectRegistry.mqh                         # 對象註冊器
```

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義基本流水線操作
- **ITradingPipelineDriver**: 驅動器介面，定義驅動器核心功能
- **TradingPipeline**: 抽象基類，實現基本流水線功能
- **TradingPipelineContainer**: 統一容器類，支持組合模式
- **TradingPipelineDriver**: 單例驅動器，系統核心控制器
- **TradingController**: EA 生命週期控制器
- **TradingErrorHandler**: 統一錯誤處理器

## 簡化架構圖

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute()
        +GetName()
        +IsExecuted()
        +Restore()
    }

    class ITradingPipelineDriver {
        <<interface>>
        +Initialize()
        +GetManager()
        +GetRegistry()
        +GetExplorer()
        +GetErrorHandler()
        +Cleanup()
    }

    %% 核心類別
    class TradingPipeline {
        <<abstract>>
        +Execute()
        +GetName()
        +GetStage()
        #Main()*
    }

    class TradingPipelineContainer {
        +AddPipeline()
        +Execute()
        +GetPipelineCount()
    }

    class TradingPipelineDriver {
        <<singleton>>
        +GetInstance()$
        +Initialize()
        +GetManager()
        +GetRegistry()
    }

    class TradingController {
        +OnInit()
        +OnTick()
        +OnDeinit()
    }

    class TradingErrorHandler {
        +AddError()
        +HandleError()
        +GetErrorCount()
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline
    ITradingPipeline <|.. TradingPipelineContainer
    ITradingPipelineDriver <|.. TradingPipelineDriver
    TradingPipelineContainer o-- ITradingPipeline
    TradingPipeline --> ITradingPipelineDriver
    TradingController --> TradingPipelineDriver
    TradingPipelineDriver --> TradingErrorHandler
```

### 2. 管理和支援組件

```mermaid
classDiagram
    class TradingPipelineContainerManager {
        +SetContainer()
        +GetContainer()
        +ExecuteAll()
        +GetContainerCount()
    }

    class TradingPipelineRegistry {
        +Register()
        +Unregister()
        +IsStageRegistered()
        +GetRegisteredStageContainer()
    }

    class TradingPipelineExplorer {
        +GetPipeline()
        +GetAllPipelinesByStage()
        +HasPipelineForStage()
        +GenerateExplorationReport()
    }

    class ObjectRegistry {
        +Register()
        +Unregister()
        +GetObject()
        +Contains()
    }

    %% 關係
    TradingPipelineDriver --> TradingPipelineContainerManager
    TradingPipelineDriver --> TradingPipelineRegistry
    TradingPipelineDriver --> TradingPipelineExplorer
    TradingPipelineDriver --> ObjectRegistry
    TradingPipelineExplorer --> TradingPipelineRegistry
    TradingPipelineRegistry --> TradingPipelineContainerManager
```

### 3. 枚舉和結果類型

```mermaid
classDiagram
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        DEINIT_CLEANUP
        ...
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class PipelineResult {
        +IsSuccess()
        +GetMessage()
        +GetTimestamp()
        +ToString()
    }

    class TradingErrorRecord {
        +m_message
        +m_errorLevel
        +m_timestamp
    }

    class TradingErrorVisitor {
        <<abstract>>
        +Visit()*
    }

    %% 關係
    PipelineResult --> ENUM_ERROR_LEVEL
    TradingErrorRecord --> ENUM_ERROR_LEVEL
    TradingErrorHandler --> TradingErrorVisitor
    TradingErrorHandler --> TradingErrorRecord
```

### 4. 完整系統架構

```mermaid
graph TB
    subgraph "EA 層"
        TC[TradingController]
    end

    subgraph "驅動器層"
        TPD[TradingPipelineDriver<br/><<singleton>>]
        TEH[TradingErrorHandler]
        OR[ObjectRegistry]
    end

    subgraph "管理層"
        TPCM[TradingPipelineContainerManager]
        TPR[TradingPipelineRegistry]
        TPE[TradingPipelineExplorer]
    end

    subgraph "容器層"
        TPC[TradingPipelineContainer]
        EP[EventPipeline]
        SP[StagePipeline]
    end

    subgraph "流水線層"
        TP[TradingPipeline<br/><<abstract>>]
        MP[MainPipeline<br/><<abstract>>]
        CP[CustomPipeline]
    end

    %% 關係
    TC --> TPD
    TPD --> TPCM
    TPD --> TPR
    TPD --> TPE
    TPD --> TEH
    TPD --> OR

    TPCM --> TPC
    TPR --> TPC
    TPE --> TPR

    TPC --> TP
    EP --> TP
    SP --> TP

    TP --> TPD
    MP --> TP
    CP --> TP
```

## 設計模式

### 核心設計模式

1. **組合模式 (Composite Pattern)**

   - TradingPipelineContainer 可包含多個子流水線
   - 統一處理單個流水線和容器流水線

2. **模板方法模式 (Template Method Pattern)**

   - TradingPipeline 定義執行流程骨架
   - 子類實現具體的 Main() 方法

3. **單例模式 (Singleton Pattern)**

   - TradingPipelineDriver 確保全局唯一實例
   - 統一管理整個流水線系統

4. **控制器模式 (Controller Pattern)**

   - TradingController 作為 EA 生命週期統一入口
   - 協調 OnInit、OnTick、OnDeinit 方法

5. **訪問者模式 (Visitor Pattern)**

   - TradingErrorHandler 實現靈活錯誤處理
   - 支持動態擴展錯誤處理策略

6. **註冊器模式 (Registry Pattern)**

   - TradingPipelineRegistry 管理流水線註冊
   - ObjectRegistry 提供對象管理功能

7. **裝飾者模式 (Decorator Pattern)**
   - LoggerDecorator 為組件添加日誌功能
   - 透明功能擴展，遵循開放/封閉原則

## 核心特性

### 主要優勢

1. **統一架構**

   - 合併 CompositePipeline 和 PipelineGroup 功能
   - 減少約 70% 重複代碼
   - 簡化架構層次

2. **高效管理**

   - HashMap 提供 O(1) 查找性能
   - 靈活的容器數量管理
   - 直觀的 API 設計

3. **階段化處理**

   - 明確的交易階段定義
   - 支持階段化流水線執行
   - 事件驅動的處理模式

4. **錯誤處理**

   - 統一的錯誤級別定義
   - 訪問者模式實現靈活處理
   - 完整的錯誤生命週期管理

5. **EA 生命週期**

   - TradingController 統一入口
   - 自動錯誤處理和狀態追蹤
   - 與流水線系統無縫集成

6. **對象管理**
   - ObjectRegistry 統一對象註冊
   - 靈活的所有權控制
   - 詳細的操作結果反饋

## 使用流程

### 推薦流程（使用 TradingController）

```mql4
// 1. 獲取驅動器實例
TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

// 2. 創建控制器
TradingController* controller = new TradingController(driver);

// 3. 註冊流水線
TradingPipelineRegistry* registry = driver.GetRegistry();
registry.Register(new MyInitPipeline());
registry.Register(new MyTickPipeline());

// 4. EA 生命週期
ENUM_INIT_RETCODE OnInit() { return controller.OnInit(); }
void OnTick() { controller.OnTick(); }
void OnDeinit(int reason) { controller.OnDeinit(reason); }
```

### 基本流程（直接使用驅動器）

```mql4
// 1. 獲取驅動器和組件
TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
TradingPipelineRegistry* registry = driver.GetRegistry();
TradingPipelineContainerManager* manager = driver.GetManager();

// 2. 創建和註冊流水線
TradingPipeline* pipeline = new MyTradingPipeline();
registry.Register(pipeline);

// 3. 執行流水線
manager.ExecuteAll();

// 4. 檢查結果
PipelineResult* result = pipeline.GetResult();
if(result.IsSuccess()) {
    Print("執行成功: ", result.GetMessage());
}
```

## 架構變化總結

### 舊架構 → 新架構

```
舊: PipelineGroupManager → PipelineGroup → CompositePipeline → ITradingPipeline
新: TradingController → TradingPipelineDriver → TradingPipelineContainer → ITradingPipeline
```

### 主要改進

- ✅ **統一架構**: 合併功能，減少 70% 重複代碼
- ✅ **高效管理**: HashMap 提供 O(1) 查找性能
- ✅ **簡化設計**: 從 4 層減少到 2 層結構
- ✅ **EA 生命週期**: TradingController 統一入口點
- 🆕 **錯誤處理**: TradingErrorHandler 統一錯誤管理
- 🆕 **對象管理**: ObjectRegistry 統一對象註冊
- 🆕 **介面抽象**: 專門的 interface/ 資料夾

## 總結

PipelineAdvance_v1 提供了一個簡潔、高效、易用的交易流水線架構，通過統一的設計模式和清晰的職責分離，大幅簡化了原有的複雜架構，同時保持了所有核心功能並增強了系統的可維護性和可擴展性。
